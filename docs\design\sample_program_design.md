# 系统功能设计文档

## 1. 业务架构

### 1.1 业务模块关系图

*这里使用mermaid流程图描述各模块间的层级关系*

```mermaid
flowchart TD
    A(A模块) --> B(B模块)
    A(A模块) --> C(C模块)
    B(B模块) --> D(D模块)
```

### 1.2 模块列表

*描述各模块的基础信息（编号、名称、英文名、英文缩写）*
*编号：文档内唯一标识，不同文档可以重复，编号以MD开头+4位数字（从0001开始）*
*名称：模块的中文名称*
*英文名：模块的英文名称，也是缩写的全称*
*英文缩写：用于生成代码时，命名包路径，如，com.xinlong.alm.dur*

| 模块编号   | 模块名称 | 模块英文名     | 英文缩写 |
| ------ | ---- | --------- | ---- |
| MD0001 | 久期模块 | duration  | dur  |
| MD0002 | 负债模块 | liability | liab |

### 1.3 数据模型

#### 1.3.1 XX模块

*描述XX模块下的表间关系及表属性信息*

##### 1.3.1.1 表间关系

*表间关系用mermaid图描述，主要用于梳理关系，使用英文描述，中文字符会报错*

```mermaid
erDiagram
t_dur_liability_cash_flow}|..|| t_dur_liability_cash_flow_summary: summary
```

##### 1.3.1.2 表名字典

*列出XX模块所有表信息*

| 表编号    | 表中文名     | 表英文名                              | 备注  |
| ------ | -------- | --------------------------------- | --- |
| TB0001 | 负债现金流表   | t_dur_liability_cash_flow         |     |
| TB0002 | 负债现金流汇总表 | t_dur_liability_cash_flow_summary |     |

##### 1.3.1.3 表集

*描述详细的表属性信息，id（默认为主键）、create_time、update_time、create_by、update_by、is_del等字段不需要描述，后续生成DDL时会自动补充*
*唯一索引：这里唯一索引是指业务字段单字段或多字段组合的唯一性，如多字段组合的情况，在所有字段唯一索引列设置为“是”*
*说明：描述字段的作用，枚举类型字段格式为描述,value1:label1[,...,valueN:labelN]，这里一定要注意格式，在后续开发步骤中会通过这里的字段枚举描述生成字段数据*

**（1）TB0001**

| 字段名                 | 数据类型    | 长度    | 允许空 | 唯一索引 | 默认值 | 说明                                                                                                                                            |
| ------------------- | ------- | ----- | --- | ---- | --- | --------------------------------------------------------------------------------------------------------------------------------------------- |
| **account_period**  | varchar | 6     | 否   | 是    | 无   | 账期,格式YYYYMM                                                                                                                                   |
| **cash_flow_type**  | char    | 2     | 否   | 是    | 无   | 现金流类型,01:流入,02:流出                                                                                                                             |
| **bp_type**         | char    | 2     | 否   | 是    | 无   | 基点类型,01:+50bp,02:-50bp,03:0bp                                                                                                                 |
| **design_type**     | varchar | 50    | 否   | 是    | 无   | 设计类型                                                                                                                                          |
| **is_short_term**   | char    | 1     | 否   | 是    | 'N' | 是否为中短期险种                                                                                                                                      |
| **actuarial_code**  | varchar | 20    | 否   | 是    | 无   | 精算代码                                                                                                                                          |
| business_code       | varchar | 20    | 否   | 否    | 无   | 业务代码                                                                                                                                          |
| product_name        | varchar | 50    | 否   | 否    | 无   | 产品名称                                                                                                                                          |
| insurance_main_type | varchar | 50    | 否   | 否    | 无   | 险种主类                                                                                                                                          |
| insurance_sub_type  | varchar | 50    | 否   | 否    | 无   | 险种细类                                                                                                                                          |
| cash_val_set        | varchar | 65535 | 否   | 否    | 无   | 现金流值集,分为1273项,格式{"0":{"date":"2025-01-01","value":0.25},"1":{"date":"2025-01-02","value":0.35},...,"1272":{"date":"2025-12-01","value":0.15}} |

**（2）TB0002**

| 字段名                  | 数据类型    | 长度    | 允许空 | 唯一索引 | 默认值 | 说明                                                                                                                                              |
| -------------------- | ------- | ----- | --- | ---- | --- | ----------------------------------------------------------------------------------------------------------------------------------------------- |
| **account_period**   | varchar | 6     | 否   | 是    | 无   | 账期,格式YYYYMM                                                                                                                                     |
| **cash_flow_type**   | char    | 2     | 否   | 是    | 无   | 现金流类型,01:流入,02:流出                                                                                                                               |
| **bp_type**          | char    | 2     | 否   | 是    | 无   | 基点类型,01:+50bp,02:-50bp,03:0bp                                                                                                                   |
| **duration_type**    | char    | 2     | 否   | 是    | 无   | 久期类型,01:修正久期,02:有效久期                                                                                                                            |
| **design_type**      | varchar | 50    | 否   | 是    | 无   | 设计类型                                                                                                                                            |
| **is_short_term**    | char    | 1     | 否   | 是    | 'N' | 是否为中短期险种                                                                                                                                        |
| cash_val_set         | varchar | 65535 | 否   | 否    | 无   | 现金流值集,分为1273项,格式{"0":{"date":"2025-01-01","value":0.25},"1":{"date":"2025-01-02","value":0.35},...,"1272":{"date":"2025-12-01","value":0.15}}   |
| present_cash_val_set | varchar | 65535 | 否   | 否    | 无   | 现金流现值值集,分为1273项,格式{"0":{"date":"2025-01-01","value":0.25},"1":{"date":"2025-01-02","value":0.35},...,"1272":{"date":"2025-12-01","value":0.15}} |

### 1.4 用例列表

*列出所有用例场景，用例可理解为用户为完成某个作业目标而执行的一系列操作的集合，包括页面跳转、接口调用。对于批处理场景，是一系列加工处理步骤的集合*

| 用例编号   | 用例名称         | 用例描述 | 模块编号   |
| ------ | ------------ | ---- | ------ |
| UC0001 | 负债现金流汇总及现值计算 |      | MD0001 |
| UC0002 | 负债久期计算       |      | MD0001 |
| UC0003 | 分账户负债现金流现值汇总 |      | MD0001 |
| UC0004 | 分账户负债久期汇总    |      | MD0001 |

## 2. 业务概念与术语

*解释专业术语，可提升AI对专业术语的理解，更好地生成代码*

| 术语  | 释义                                                                     |
| --- | ---------------------------------------------------------------------- |
| 久期  | 久期（Duration）是债券价格对利率变化敏感度的核心衡量指标，它不仅反映了债券现金流的加权平均回收时间，也是投资者管理利率风险的重要工具 |

## 3. 功能需求

### 3.1 XX模块

#### 3.1.1 页面描述

#### 3.1.1.1 页面列表

| 页面编号   | 页面名称     | 类型  | 依赖的主页面编号 |
| ------ | -------- | --- | -------- |
| PG0001 | 现金流查询列表页 | 主页面 |          |
| PG0002 | 现金流详情页   | 弹层  | PG0002   |

#### 3.1.1.2 现金流查询列表页(PG0001)

##### 3.1.1.2.1 原型图

*这里放原型的图片，因为MD格式不存储图片，需要指定工程内部图片的相对路径*
![image-20250410001806017](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20250410001806017.png)

##### 3.1.1.2.2 输入区

| 字段名   | 字段类型 | 格式   | 备注              |
| ----- | ---- | ---- | --------------- |
| 账期    | 输入框  | 长度20 | 日期选择器,格式为YYYYMM |
| 现金流类型 | 下拉框  | 长度20 | 可选值未显示          |
| 精算代码  | 输入框  | 长度20 | 无               |
| 业务代码  | 输入框  | 长度20 | 无               |
| 久期类型  | 下拉框  |      |                 |
| 险种主类  | 下拉框  |      |                 |
| 设计类型  | 下拉框  |      |                 |
| 险种细类  | 下拉框  |      |                 |

##### 3.1.1.2.3 展示区

| 字段名   | 字段类型 | 格式  | 备注                |
| ----- | ---- | --- | ----------------- |
| 账期    | 文本   |     | 表格列,显示格式YYYYMM    |
| 现金流类型 | 文本   |     | 表格列,显示现金流类别       |
| 精算代码  | 文本   |     | 表格列,显示代码,如AC001   |
| 业务代码  | 文本   |     | 表格列,显示代码,如BC001   |
| 日期    | 日期   |     | 表格列,格式为YYYY-MM-DD |
| 久期类型  | 文本   |     | 表格列,显示久期分类,如"中期"  |
| 1     | 数值   |     | 表格列,显示金额,有千位分隔符   |
| 2     | 数值   |     | 表格列,显示金额,有千位分隔符   |
| ...   | ...  |     | ...               |
| 1272  | 数值   |     | 表格列,显示金额,有千位分隔符   |
| 操作    | 按钮组  |     | 包含编辑、删除按钮         |

##### 3.1.1.2.4 其他描述

*这里描述除输入区、展示区外其他静态页面元素*

#### 3.1.2 接口功能

##### 3.1.2.1 XX接口(IF0001)

##### 3.1.2.1.1 接口功能概述

*描述接口概要信息*

##### 3.1.2.1.2 接口基本信息

| 类型           | 描述               |
| ------------ | ---------------- |
| Method       | POST             |
| Content-Type | application/json |
| Uri          | /api/saveXxInfo  |

##### 3.1.2.1.3 接口入参

*数据类型请参考附录1，参数最好能提供示例，这样AI更容易理解数据结构*

| 参数名       | 类型      | 是否必填 | 描述   |
| --------- | ------- | ---- | ---- |
| name      | string  | 是    | 名称   |
| users     | array   | 是    | 用户数组 |
| [].id     | number  | 是    | 用户ID |
| [].name   | string  | 是    | 用户名  |
| user      | object  | 是    | 用户对象 |
| user.id   | integer | 是    | 用户ID |
| user.name | string  | 是    | 用户名  |

**【示例】：**

```json
{
    "name": "小明",
    "users": [{
            "id": 11111,
            "name": "小武"
        },
        {
            "id": 22222,
            "name": "小刘"
        }
    ],
    "user": {"id": 33333, "name": "小强"}
}
```

##### 3.1.2.1.4 接口出参

| 参数名  | 类型      | 描述             |
| ---- | ------- | -------------- |
| code | integer | 代码为200为成功,反则异常 |
| msg  | string  | 异常描述           |

**【成功示例】:** 

```json
{
    "code": 200,
    "msg": ""
}
```

**【失败示例】:**

```json
{
    "code": 500,
    "msg": "系统异常,请联系管理员!"
}
```

##### 3.1.2.1.5 接口功能详述

*描述接口实现步骤，描述方法请参考附录2*

#### 3.1.3 用例描述

##### 3.1.3.1 现金流现值计算

| 用例编号 | UC + 4位数字从1开始 |
|:----:| ------------- |
| 类型   | 批处理/服务        |
| 用例名称 |               |
| 参与者  | 系统/XX角色       |
| 页面   | 填写页面编号，逗号分隔   |
| 关联表  | 填写表编号，逗号分隔    |
| 前置用例 | 填写用例编号，逗号分隔   |

这里描述用例实现步骤，描述方法请参考附录2

**【示例}：**

| 用例编号 | UC0001        |
|:----:| ------------- |
| 类型   | 批处理           |
| 用例名称 | 负债现金流现值计算     |
| 参与者  | 系统            |
| 页面   |               |
| 关联表  | TB0001,TB0002 |
| 前置用例 |               |

**步骤1. 加载因子表**
(1)  按账期读取 TB0003 表，使用 account_period|factor_type|bp_type|duration_type 作为键，factor_val_set 作为值，构建因子 HashMap，用于后续快速查询。

**步骤2. 现金流汇总**
(1) 遍历 TB0001 表，按账期读取现金流记录：

- 键：使用account_period|cash_flow_type|bp_type|duration_type|design_type|is_short_term 拼接

- 值：以cash_val_set字段构建两个映射表：
  
  * valueMap：格式为 {"0":123.00, "1":123.00, ..., "1272":123.00}，存储现金流数值
  
  * dateMap：格式为 {"0":"2025-01-01", "1":"2025-01-02", ..., "1272":"2025-12-01"}，存储对应日期

(2) 数据聚合规则：

- 对相同键的记录，按序号累加 valueMap 中的数值（如 {"0":100, "1":200} + {"0":50, "1":50} 合并为 {"0":150, "1":250}）

- 输出结果：
  
  * 汇总后的 valueMap 和 dateMap 传递至现金流现值计算环节

**步骤 3. 计算现金流现值**
(1) 遍历规则： 逐笔处理 valueMap 中的记录
(2) 关键定义：

- cash[i]：第 i 个序号对应的现金流数值（来自 valueMap）

- fac[j]：因子值（通过 account_period|factor_type|bp_type|duration_type 从因子 HashMap 查询）

- factor_type 取值逻辑：
  
  * 当 design_type 为 "传统险"/"分红险"/"万能险" 且 is_short_term=Y → 01
  
  * 当 design_type="万能险" 且 is_short_term=N → 01
  
  * 其他情况 → 02

(3) 现值计算公式：

- 现金流为流入的场景（公式 1）：
  
  * present_value[i] = ∑[j=i+1,1272] cash[j] × fac[j−i−1]（i 从 0 开始，且present_value[1272]=0）

- 现金流为流出的场景（公式 2）：
  
  * present_value[i] = ∑[j=i,1272] cash[j] × fac[j−i]（i 从 0 开始）

(4) 计算规范：

- 结果精度： 保留 8 位小数（计算过程保留 16 位小数，四舍五入）
- 数据类型：使用 BigDecimal 替代 Double 确保精度

**步骤 4. 数据入表**
(1) 将计算结果存入 TB0005 表，字段映射规则：

- present_cash_val_set 中的日期按序号取自 dateMap
- 每条记录包含原始现金流和对应的现值计算结果

# 附录1

## 接口请求参数类型：

| 类型       | 说明                      | 示例                             |
| -------- | ----------------------- | ------------------------------ |
| string   | 文本类型，需定义长度限制            | "username": "john_doe"         |
| number   | 包括整数和浮点数                | "age": 25                      |
| boolean  | 布尔值（true/false）         | "is_active": true              |
| array    | 数组，需声明元素类型              | "tags": ["urgent", "feature"]  |
| object   | 键值对嵌套结构                 | "address": {"city": "Beijing"} |
| date     | 日期（YYYY-MM-DD）          | "2025-06-19"                   |
| datetime | 时间（YYYY-MM-DD HH:MM:SS） | "2025-06-19 14:30:00"          |
| file     | 文件上传                    | 文件二进制数据                        |

# 附录2

## 1. 功能逻辑表达方法：

### 1.1 面向对象法

**核心原则**：以数据载体为核心构建逻辑描述，确保 AI 可精准解析数据流向

- 必须明确指定数据操作对象（表 / 缓存 / 文件），避免模糊动作词汇
- 采用 "对象 + 字段 + 操作" 的三元组结构，示例：`从TB0001表查询字段A，对字段B执行聚合操作后写入TB0002表`

**【正例】：**
**步骤1.** 现金流汇总
以账期为202412作为条件查询TB0001表，以账期,现金流类型,基点类型,久期类型,设计类型,是否中短期作为汇总字段,针对cash_val_set的value值按对应序号进行加合汇总，所有数据汇总完成后写入TB0002表
**说明：**

- 这里所面向的对象是表TB0001和TB0002，AI会自动在文档中检索表名并转换为英文名
- 可以直接使用字段的中文名称进行描述，AI会自动转为字段英文名生成代码和SQL，但中文名可能作为前缀出现在文档的非字段描述位置，为了避免识别问题，建议使用英文名描述
- 不需要详细描述DTO和Entity赋值逻辑，AI会自动识别赋值并写入对应表

**【反例】：**
**步骤1.** 现金流汇总
请对账期为202412的现金流数据实现汇总
**说明：**

- 没有描述清楚数据来源
- 没有描述清楚数据加工方法
- 没有描述结果数据如何处理

### 1.2 符号引用法

* 前面的面向对象的方法中可以看到，表名使用TB0001和TB0002这样的符号或者叫编号，这种方式是符号引用法。通过符号引用可以减少字数，使逻辑表达更清晰，符号也具有唯一性，不会出现AI引用位置出错的问题，如中文容易出现引用错误。文档中具有符号的对象有模块、表、接口、用例、页面等，AI可以通过符号查找到对应对象的描述信息。

| 对象类型 | 符号格式     | 示例     |
| ---- | -------- | ------ |
| 数据表  | TB+4 位数字 | TB0001 |
| 接口   | IF+4 位数字 | IF0002 |
| 模块   | MD+4 位数字 | MD0003 |
| 用例   | UC+4 位数字 | UC0004 |
| 页面   | PG+4 位数字 | PG0004 |

**符号引用优势**：

- 唯一性：避免中文同名对象歧义（如 "用户表" 可能指表TB0005，也可能是“用户表达意思”的前缀并不表示某张表）
- 轻量化：符号长度固定，提升文档可读性
- 机器可解析：AI 可通过符号直接关联元数据定义

### 1.3 公式表达法

- 对于涉及复杂计算的场景，可以通过变量定义 + 公式方式描述计算逻辑

**【示例】：**

- i ∈ [0,1272]
- 现金流金额[i] = TB0002.cash_val_set[i].value
- 现金流现值[i] = TB0002.present_cash_val_set[i].valueue
- 折现因子[i] = TB0003.factor_val_set[i].value
- 久期值[i] = (∑[j=i+1,1272] 现金流金额[j] \* 折现因子[j-i-1] \* (j-1/12) / (1 + 折现率[j-i-1])) / 现金流现值[i]

**说明：**

- 变量定义需包含数据来源（表名 + 字段路径）
- 公式中特殊符号需使用标准 LaTeX 格式（如∑表示求和）
- 涉及数组操作时需明确索引范围

### 1.4 标签定义法

- 对于跨记录或不同数据集间的计算场景，可以给不同数据集打上标签，再描述数据集间的关联关系，最后把数据集标签带入公式来描述计算过程

**【示例】：**
有效久期计算：
(1) 数据集标注：

- A=TB0002 (账期 = 202412,duration_type = 有效久期,bp_type=+50bp)
- B=TB0002 (账期 = 202412,duration_type = 有效久期,bp_type=-50bp)
- C=TB0002 (账期 = 202412,duration_type = 修正久期,bp_type=0bp)

(2) 关联规则：

- A/B/C 通过 [cash_flow_type,design_type,is_short_term] 进行JOIN

(3) 计算公式: 

- duration_value[i]=(B.present_cash_val_set[i].value-A.present_cash_val_set[i].value)/0.01/C.present_cash_val_set[i].value,i从0开始至1272

### 1.5 SQL表达法

- 对于复杂的表关联场景，建议直接使用SQL方式描述。如果用自然语言，不仅不好表达，反而更耗时间。

### 1.6 分层分步表达法

- 在描述功能逻辑时，因涉及多步骤、多层级，可按以下分层分步方式清晰呈现：

```textile
{主步骤}：{主题描述}
({子步骤编号}) {子步骤主题}
  - {操作项1}：{详细描述}
  - {操作项2}：{详细描述}
    * {子操作1}：{技术细节}
    * {子操作2}：{技术细节}
```

**【示例】：**

```context
步骤 1：搜索请求处理
(1) 前端交互与请求发送
- 用户在电商系统前端搜索栏输入关键词（如商品名称、品类 ），点击搜索按钮后，前端页面封装搜索参数（含关键词、用户筛选条件等 ），通过 HTTP 协议向服务端发送搜索请求。
(2) 网关层请求转发
- 服务端网关接收到前端请求，校验请求合法性（如参数格式、用户身份 token 有效性 ），若合法则根据系统路由规则，将请求转发至商品搜索服务对应的业务模块。
步骤 2：搜索逻辑执行与结果返回
(1) 搜索业务逻辑处理
- 商品搜索服务模块接收请求后，先从缓存（如 Redis ）查询是否有匹配关键词的热门搜索结果缓存，若有且未过期则直接使用；若缓存无有效数据，再访问数据库（如 MySQL ），通过 SQL 语句（结合全文检索插件如 Elasticsearch 协同），根据关键词、筛选条件检索商品数据，进行数据聚合、排序（按销量、价格、新品等规则 ）。
(2) 结果封装与响应
- 搜索服务将处理后的商品数据（含商品基本信息、价格、库存状态等 ），按前端可解析的格式（如 JSON ）封装，通过网关返回给前端；前端接收后，渲染展示搜索结果列表，供用户浏览选择 。
```

## 2. 设计文档编写原则

### 2.1 尽可能使用简洁的文字描述功能逻辑

### 2.2 一句话只描述单一的功能逻辑

### 2.3 按模块间关联性拆分设计文档

### 2.4 通过AI生成90%以上代码

### 2.5 逻辑描述一定要清晰并可实现

## 3. 图表

### 3.1 流程图

**作用：**

* 以可视化方式直观呈现功能逻辑的完整脉络，让复杂逻辑结构清晰可辨

* 支持在反复迭代优化中动态调整逻辑链路，提升方案打磨效率

* 为AI生成代码提供结构化逻辑框架，辅助AI理解业务逻辑层级

**场景：**

* 当功能逻辑涉及多模块交互、分支条件或时序依赖，单纯通过文字描述难以梳理逻辑闭环时，可借助流程图进行具象化表达，同时结合文字详细描述每个节点的具体逻辑

**【示例】：**

```mermaid
flowchart TD
    A[用户登录] --> B{选择课程}
    B -->|免费课程| C[开始学习]
    B -->|付费课程| D{检查会员状态}
    D -->|是会员| C
    D -->|非会员| E[购买课程]
    E --> C

    C --> F{课程完成?}
    F -->|否| G[继续学习]
    G --> C
    F -->|是| H[参加测验]

    H --> I{测验通过?}
    I -->|是| J[获得证书]
    I -->|否| K[复习课程]
    K --> C

    J --> L[分享成绩]
    L --> M[推荐新课程]
    M --> B
```

### 3.2 协作图

**作用：**

* 清晰呈现各状态间的流转关系

**场景：**

* 适合审批状态、业务流转状态等状态描述场景

**【示例】：**

```mermaid
stateDiagram-v2
[*] --> Idle
Idle --> Processing : Start
Processing --> Success : Success Event
Processing --> Error : Error Event
Error --> Processing : Retry
Error --> Idle : Cancel
Success --> [*]
Idle --> [*]
```