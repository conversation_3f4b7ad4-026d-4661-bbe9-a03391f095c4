# 源数据及报表管理 - 批量导入导出功能需求设计文档

## 1. 概述

### 1.1 功能描述
本功能主要针对"源数据及报表管理"菜单下的源数据批量导入以及报表批量导出功能，提供统一的数据导入导出管理界面，支持多表数据的批量处理和状态跟踪。

### 1.2 业务背景
- 系统需要支持多个模块的数据表批量导入功能
- 需要统一管理各类报表的批量导出功能
- 提供导入导出操作的状态跟踪和错误信息记录
- 支持基于积木报表的多表合并导出

### 1.3 适用范围
- 资产宽表模块(AST)
- 资产配置状态模块(ASM)
- 资产信用状态模块(ACM)
- 久期分析模块(ADUR)
- 成本核算模块(COST)
- 现金流测试模块(CFT)
- 最低资本模块(MINC)
- 负债产品模块(LIAB)

## 2. 功能需求

### 2.1 页面布局设计

#### 2.1.1 搜索条件区域
- **类型**：下拉选择框，选项包括"导入"、"导出"
- **状态**：下拉选择框，选项包括"成功"、"失败"、"处理中"
- **备注**：文本输入框，支持模糊查询
- **操作时间**：日期范围选择器

#### 2.1.2 操作按钮区域
- **导入**：触发批量数据导入功能
- **导出**：触发批量报表导出功能
- **刷新**：刷新当前页面数据
- **清空**：清空搜索条件

#### 2.1.3 数据展示区域
表格字段包括：
- **ID**：操作记录唯一标识
- **类型**：导入/导出
- **状态**：成功/失败/处理中
- **操作时间**：操作发起时间
- **完成时间**：操作完成时间
- **处理文件**：导入的文件名或导出的文件名
- **账期**：相关的账期信息
- **备注**：详细的操作信息或错误信息
- **操作**：查看详情、下载文件等操作按钮

### 2.2 导入功能需求

#### 2.2.1 文件格式要求
- **文件类型**：Excel文件(.xlsx, .xls)
- **Sheet命名规则**：`模块英文缩写_表名`
  - 示例：`ast_资产整体明细表`、`acm_信用评级表`、`asm_偿付能力状况表`
- **支持多Sheet**：单个Excel文件可包含多个Sheet，每个Sheet对应一个数据表

#### 2.2.2 导入流程设计
1. **文件上传**：用户选择Excel文件进行上传
2. **Sheet解析**：系统解析文件中的所有Sheet
3. **表名映射**：根据Sheet名称中的表名，通过配置表确定目标菜单和数据表
4. **数据验证**：对每个Sheet的数据进行格式和业务规则验证
5. **字典转换**：将Excel中的中文标签转换为系统字典编码
6. **数据导入**：将验证通过的数据批量插入到对应的数据表
7. **结果反馈**：记录导入结果，包括成功数量和失败详情

#### 2.2.3 错误处理策略
- **失败即停止**：任何Sheet导入失败时，停止整个导入过程
- **详细错误信息**：记录具体的失败原因，包括：
  - Sheet名称
  - 行号和列号
  - 具体错误原因
  - 数据值信息

### 2.3 导出功能需求

#### 2.3.1 导出流程设计
1. **账期选择**：用户输入或选择要导出的账期
2. **报表查询**：系统根据账期查询所有相关报表数据
3. **数据整合**：将多个报表数据整合到一个Excel文件的不同Sheet中
4. **文件生成**：生成Excel文件并提供下载
5. **记录日志**：记录导出操作的详细信息

#### 2.3.2 导出文件格式
- **文件命名**：`报表数据_账期_导出时间.xlsx`
- **Sheet命名**：与导入时的命名规则保持一致
- **数据格式**：
  - 字典编码转换为中文标签
  - 数值字段保留适当的小数位数
  - 日期字段使用中文格式

## 3. 数据库设计

### 3.1 导入导出操作记录表

```sql
-- 导入导出操作记录表
CREATE TABLE `t_data_import_export_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `operation_type` varchar(10) NOT NULL COMMENT '操作类型：IMPORT-导入，EXPORT-导出',
  `status` varchar(20) NOT NULL COMMENT '状态：SUCCESS-成功，FAILED-失败，PROCESSING-处理中',
  `accounting_period` varchar(6) DEFAULT NULL COMMENT '账期，格式YYYYMM',
  `file_name` varchar(255) DEFAULT NULL COMMENT '文件名称',
  `file_path` varchar(500) DEFAULT NULL COMMENT '文件存储路径',
  `total_records` int(11) DEFAULT 0 COMMENT '总记录数',
  `success_records` int(11) DEFAULT 0 COMMENT '成功记录数',
  `failed_records` int(11) DEFAULT 0 COMMENT '失败记录数',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `error_message` mediumtext DEFAULT NULL COMMENT '错误信息',
  `remark` varchar(1000) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_status` (`status`),
  KEY `idx_accounting_period` (`accounting_period`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='导入导出操作记录表';
```

### 3.2 表名映射配置表

```sql
-- 表名映射配置表
CREATE TABLE `t_table_mapping_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `table_name` varchar(100) NOT NULL COMMENT '表名（中文）',
  `table_code` varchar(100) NOT NULL COMMENT '表编码（英文）',
  `module_code` varchar(20) NOT NULL COMMENT '模块编码',
  `menu_path` varchar(200) DEFAULT NULL COMMENT '菜单路径',
  `entity_class` varchar(200) DEFAULT NULL COMMENT '实体类名',
  `import_enabled` tinyint(1) DEFAULT 1 COMMENT '是否支持导入，0:否，1:是',
  `export_enabled` tinyint(1) DEFAULT 1 COMMENT '是否支持导出，0:否，1:是',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_table_name` (`table_name`),
  UNIQUE KEY `uk_table_code` (`table_code`),
  KEY `idx_module_code` (`module_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='表名映射配置表';
```

### 3.3 字典映射配置表

```sql
-- 字典映射配置表
CREATE TABLE `t_dict_mapping_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `table_code` varchar(100) NOT NULL COMMENT '表编码',
  `field_name` varchar(100) NOT NULL COMMENT '字段名',
  `dict_type` varchar(100) NOT NULL COMMENT '字典类型',
  `is_required` tinyint(1) DEFAULT 0 COMMENT '是否必填，0:否，1:是',
  `default_value` varchar(100) DEFAULT NULL COMMENT '默认值',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_table_field` (`table_code`, `field_name`),
  KEY `idx_dict_type` (`dict_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='字典映射配置表';
```

### 3.4 积木报表配置表

```sql
-- 积木报表配置表
CREATE TABLE `t_jmreport_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `report_name` varchar(100) NOT NULL COMMENT '报表名称',
  `report_code` varchar(100) NOT NULL COMMENT '报表编码',
  `excel_config_id` varchar(100) NOT NULL COMMENT '积木报表配置ID',
  `module_code` varchar(20) NOT NULL COMMENT '模块编码',
  `export_enabled` tinyint(1) DEFAULT 1 COMMENT '是否支持导出，0:否，1:是',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_report_code` (`report_code`),
  UNIQUE KEY `uk_excel_config_id` (`excel_config_id`),
  KEY `idx_module_code` (`module_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积木报表配置表';
```

## 4. 配置数据初始化

### 4.1 表名映射配置数据

```sql
-- 插入表名映射配置数据
INSERT INTO `t_table_mapping_config` (`table_name`, `table_code`, `module_code`, `menu_path`, `entity_class`, `import_enabled`, `export_enabled`, `sort_order`) VALUES
('资产整体明细表', 't_ast_asset_detail_overall', 'AST', '/ast/assetDetail', 'AssetDetailOverallEntity', 1, 1, 1),
('资产基础配置表', 't_ast_asset_basic_config', 'AST', '/ast/assetBasicConfig', 'AssetBasicConfigEntity', 1, 1, 2),
('账户名称映射表', 't_ast_account_name_map', 'AST', '/ast/accountNameMap', 'AccountNameMapEntity', 1, 1, 3),
('VaR值分析表', 't_ast_var_analysis', 'AST', '/ast/varAnalysis', 'VarAnalysisEntity', 1, 1, 4),
('信用评级表', 't_acm_credit_rating', 'ACM', '/acm/creditRating', 'CreditRatingEntity', 1, 1, 5),
('偿付能力状况表', 't_asm_solvency_status', 'ASM', '/asm/solvencyStatus', 'SolvencyStatusEntity', 1, 1, 6),
('折现曲线表', 't_adur_discount_curve', 'ADUR', '/adur/discountCurve', 'DiscountCurveEntity', 1, 1, 7),
('成本核算汇总表', 't_cost_account_summary', 'COST', '/cost/accountSummary', 'AccountSummaryEntity', 1, 1, 8);
```

### 4.2 字典映射配置数据

```sql
-- 插入字典映射配置数据
INSERT INTO `t_dict_mapping_config` (`table_code`, `field_name`, `dict_type`, `is_required`, `default_value`) VALUES
('t_ast_asset_detail_overall', 'account_name', 'ast_account_name_mapping', 1, NULL),
('t_ast_asset_detail_overall', 'asset_sub_sub_category', 'asset_sub_sub_category', 1, NULL),
('t_ast_asset_detail_overall', 'bond_type', 'bond_type', 0, NULL),
('t_ast_asset_detail_overall', 'accounting_classification', 'accounting_classification', 0, NULL),
('t_ast_asset_detail_overall', 'credit_rating', 'credit_rating', 0, NULL),
('t_ast_asset_basic_config', 'asset_sub_sub_category', 'asset_sub_sub_category', 1, NULL),
('t_ast_asset_basic_config', 'calculable_cashflow_flag', 'yes_no_flag', 0, '0'),
('t_ast_asset_basic_config', 'credit_rating_logic_flag', 'credit_rating_logic_flag', 0, '0'),
('t_acm_credit_rating', 'credit_rating_category', 'credit_rating_category', 1, NULL),
('t_asm_solvency_status', 'solvency_category', 'solvency_category', 1, NULL);
```

### 4.3 积木报表配置数据

```sql
-- 插入积木报表配置数据
INSERT INTO `t_jmreport_config` (`report_name`, `report_code`, `excel_config_id`, `module_code`, `export_enabled`, `sort_order`) VALUES
('资产整体明细表', 'ast_asset_detail_overall', '1087231279847813120', 'AST', 1, 1),
('资产基础配置表', 'ast_asset_basic_config', '1087231279847813121', 'AST', 1, 2),
('VaR值分析表', 'ast_var_analysis', '1087231279847813122', 'AST', 1, 3),
('信用评级表', 'acm_credit_rating', '1087231279847813123', 'ACM', 1, 4),
('偿付能力状况表', 'asm_solvency_status', '1087231279847813124', 'ASM', 1, 5),
('折现曲线表', 'adur_discount_curve', '1087231279847813125', 'ADUR', 1, 6),
('成本核算汇总表', 'cost_account_summary', '1087231279847813126', 'COST', 1, 7);
```

## 5. 技术实现方案

### 5.1 后端技术架构

#### 5.1.1 核心组件设计
- **ImportExportController**：控制器层，处理导入导出请求
- **ImportExportService**：业务逻辑层，实现导入导出核心功能
- **TableMappingConfigService**：表映射配置服务
- **DictMappingConfigService**：字典映射配置服务
- **JmreportConfigService**：积木报表配置服务
- **ExcelProcessor**：Excel文件处理器
- **DictConverter**：字典转换器

#### 5.1.2 导入处理流程
1. **文件解析**：使用EasyExcel解析上传的Excel文件
2. **Sheet识别**：根据Sheet名称识别对应的数据表
3. **配置查询**：查询表映射配置和字典映射配置
4. **数据转换**：将Excel数据转换为实体对象
5. **数据验证**：执行业务规则验证
6. **批量插入**：使用MyBatis-Plus批量插入数据
7. **日志记录**：记录操作日志和错误信息

#### 5.1.3 导出处理流程
1. **配置查询**：查询积木报表配置信息
2. **数据查询**：调用积木报表API获取数据
3. **数据转换**：将字典编码转换为中文标签
4. **文件生成**：使用EasyExcel生成多Sheet的Excel文件
5. **文件存储**：将生成的文件存储到指定位置
6. **日志记录**：记录导出操作日志

### 5.2 前端技术架构

#### 5.2.1 页面组件设计
- **ImportExportIndex.vue**：主页面组件
- **ImportDialog.vue**：导入对话框组件
- **ExportDialog.vue**：导出对话框组件
- **DetailDialog.vue**：详情查看对话框组件

#### 5.2.2 状态管理
- 使用Vuex管理导入导出操作状态
- 实现操作进度的实时更新
- 支持操作结果的状态展示

### 5.3 文件存储方案

#### 5.3.1 存储目录结构
```
/data/alm/import_export/
├── import/
│   ├── 202401/
│   │   ├── success/
│   │   └── failed/
│   └── 202402/
└── export/
    ├── 202401/
    └── 202402/
```

#### 5.3.2 文件命名规范
- **导入文件**：`import_原文件名_时间戳.xlsx`
- **导出文件**：`export_账期_时间戳.xlsx`
- **错误日志**：`error_操作ID_时间戳.log`

## 6. 接口设计

### 6.1 导入相关接口

#### 6.1.1 批量导入接口
```
POST /api/import-export/import
Content-Type: multipart/form-data

参数：
- file: Excel文件
- accountingPeriod: 账期（可选）
- updateSupport: 是否支持更新（可选，默认false）

响应：
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "operationId": "*********",
    "status": "PROCESSING",
    "totalSheets": 5,
    "processedSheets": 0
  }
}
```

#### 6.1.2 导入状态查询接口
```
GET /api/import-export/import/status/{operationId}

响应：
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "operationId": "*********",
    "status": "SUCCESS",
    "totalRecords": 1000,
    "successRecords": 950,
    "failedRecords": 50,
    "errorMessage": "部分数据导入失败，详见错误日志"
  }
}
```

### 6.2 导出相关接口

#### 6.2.1 批量导出接口
```
POST /api/import-export/export

参数：
{
  "accountingPeriod": "202401",
  "reportCodes": ["ast_asset_detail_overall", "acm_credit_rating"],
  "exportFormat": "EXCEL"
}

响应：
{
  "code": 200,
  "message": "导出任务已创建",
  "data": {
    "operationId": "*********",
    "status": "PROCESSING"
  }
}
```

#### 6.2.2 文件下载接口
```
GET /api/import-export/download/{operationId}

响应：文件流
```

### 6.3 配置管理接口

#### 6.3.1 表映射配置查询
```
GET /api/import-export/config/table-mapping

响应：
{
  "code": 200,
  "message": "查询成功",
  "data": [
    {
      "tableName": "资产整体明细表",
      "tableCode": "t_ast_asset_detail_overall",
      "moduleCode": "AST",
      "importEnabled": true,
      "exportEnabled": true
    }
  ]
}
```

## 7. 异常处理机制

### 7.1 导入异常处理

#### 7.1.1 文件格式异常
- **异常类型**：文件格式不支持、文件损坏
- **处理方式**：返回明确的错误信息，提示用户重新上传
- **错误代码**：FILE_FORMAT_ERROR

#### 7.1.2 Sheet名称异常
- **异常类型**：Sheet名称不符合规范、找不到对应的表配置
- **处理方式**：列出所有不符合规范的Sheet名称，提供正确的命名示例
- **错误代码**：SHEET_NAME_ERROR

#### 7.1.3 数据验证异常
- **异常类型**：必填字段为空、数据格式错误、字典值不存在
- **处理方式**：记录详细的错误位置和原因，支持批量错误展示
- **错误代码**：DATA_VALIDATION_ERROR

#### 7.1.4 业务规则异常
- **异常类型**：数据重复、外键约束违反、业务逻辑冲突
- **处理方式**：提供具体的业务规则说明和修正建议
- **错误代码**：BUSINESS_RULE_ERROR

### 7.2 导出异常处理

#### 7.2.1 数据查询异常
- **异常类型**：账期数据不存在、查询超时
- **处理方式**：提示用户检查账期参数，支持重试机制
- **错误代码**：DATA_QUERY_ERROR

#### 7.2.2 文件生成异常
- **异常类型**：磁盘空间不足、文件写入失败
- **处理方式**：清理临时文件，提示系统管理员检查存储空间
- **错误代码**：FILE_GENERATION_ERROR

## 8. 性能优化方案

### 8.1 导入性能优化

#### 8.1.1 批量处理优化
- **批次大小**：每批处理1000条记录，避免内存溢出
- **并行处理**：支持多Sheet并行导入，提高处理效率
- **事务管理**：使用分批事务，减少锁定时间

#### 8.1.2 内存优化
- **流式读取**：使用EasyExcel的流式读取，避免大文件内存溢出
- **对象复用**：复用数据转换对象，减少GC压力
- **缓存策略**：缓存字典数据和配置信息，减少数据库查询

### 8.2 导出性能优化

#### 8.2.1 数据查询优化
- **分页查询**：大数据量时使用分页查询，避免一次性加载过多数据
- **索引优化**：确保查询字段有合适的索引
- **查询缓存**：对相同账期的查询结果进行缓存

#### 8.2.2 文件生成优化
- **异步处理**：导出任务异步执行，避免前端长时间等待
- **压缩存储**：对大文件进行压缩存储，节省存储空间
- **CDN加速**：文件下载使用CDN加速，提高下载速度

## 9. 安全控制

### 9.1 权限控制
- **功能权限**：基于角色的功能权限控制
- **数据权限**：基于部门和用户的数据权限控制
- **操作审计**：记录所有导入导出操作的审计日志

### 9.2 文件安全
- **文件类型检查**：严格检查上传文件的类型和内容
- **文件大小限制**：限制上传文件的最大大小（建议100MB）
- **病毒扫描**：对上传文件进行病毒扫描
- **文件加密**：敏感文件进行加密存储

### 9.3 数据安全
- **SQL注入防护**：使用参数化查询，防止SQL注入
- **数据脱敏**：导出时对敏感数据进行脱敏处理
- **访问控制**：限制文件访问权限，防止未授权访问

## 10. 监控与运维

### 10.1 监控指标
- **操作成功率**：导入导出操作的成功率统计
- **处理时间**：操作处理时间的监控和告警
- **文件大小**：处理文件大小的统计分析
- **错误频率**：各类错误的发生频率统计

### 10.2 日志管理
- **操作日志**：记录所有用户操作的详细日志
- **错误日志**：记录系统错误和异常信息
- **性能日志**：记录关键操作的性能数据
- **审计日志**：记录敏感操作的审计信息

### 10.3 运维支持
- **健康检查**：提供系统健康检查接口
- **配置管理**：支持动态配置修改，无需重启系统
- **故障恢复**：提供数据恢复和回滚机制
- **容量规划**：根据使用情况进行容量规划和扩容

## 11. 测试方案

### 11.1 功能测试
- **导入功能测试**：测试各种文件格式和数据场景的导入功能
- **导出功能测试**：测试不同账期和报表组合的导出功能
- **异常处理测试**：测试各种异常情况的处理机制
- **权限控制测试**：测试不同角色的权限控制效果

### 11.2 性能测试
- **大文件处理测试**：测试大文件（50MB+）的处理能力
- **并发处理测试**：测试多用户同时操作的并发处理能力
- **内存使用测试**：测试系统内存使用情况和优化效果
- **响应时间测试**：测试各接口的响应时间是否符合要求

### 11.3 安全测试
- **文件上传安全测试**：测试恶意文件上传的防护效果
- **权限绕过测试**：测试权限控制的安全性
- **数据泄露测试**：测试数据访问的安全控制
- **SQL注入测试**：测试SQL注入防护的有效性

## 12. 部署方案

### 12.1 环境要求
- **JDK版本**：JDK 8+
- **数据库**：MySQL 5.7+
- **存储空间**：至少100GB可用空间
- **内存要求**：建议8GB+内存

### 12.2 配置参数
```yaml
# 导入导出配置
import-export:
  # 文件存储路径
  file-storage-path: /data/alm/import_export
  # 最大文件大小（MB）
  max-file-size: 100
  # 批处理大小
  batch-size: 1000
  # 异步任务线程池大小
  async-pool-size: 10
  # 文件保留天数
  file-retention-days: 30
```

### 12.3 部署步骤
1. **数据库初始化**：执行DDL脚本创建相关表
2. **配置数据初始化**：插入基础配置数据
3. **应用部署**：部署后端应用和前端页面
4. **权限配置**：配置相关功能的权限
5. **测试验证**：执行功能测试确保部署成功

---

**文档版本**：v1.0
**创建日期**：2025-01-07
**最后更新**：2025-01-07
**文档状态**：待评审